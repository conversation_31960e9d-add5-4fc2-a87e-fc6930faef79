
using MarketDataService.Modules.Brokers.Interfaces;
using MarketDataService.Modules.Brokers.Services;
using MarketDataService.Modules.Symbols.Interfaces;
using MarketDataService.Modules.Pricing.Interfaces;

namespace MarketDataService.Modules.Brokers;

public static class BrokersModule
{
    public static IServiceCollection AddBrokersModule(this IServiceCollection services)
    {
        // Register external data providers
        services.AddHttpClient<FinnhubProvider>();
        services.AddHttpClient<PolygonProvider>();

        // Register FinnhubProvider for all interfaces
        services.AddSingleton<IPriceProvider, FinnhubProvider>();
        services.AddSingleton<IHistoricalPriceProvider, FinnhubProvider>();
        services.AddSingleton<IBrokerMetadataProvider, FinnhubProvider>();
        services.AddSingleton<ISymbolProvider, FinnhubProvider>();

        // Register PolygonProvider for all interfaces
        services.AddSingleton<IPriceProvider, PolygonProvider>();
        services.AddSingleton<IHistoricalPriceProvider, PolygonProvider>();
        services.AddSingleton<IBrokerMetadataProvider, PolygonProvider>();
        services.AddSingleton<ISymbolProvider, PolygonProvider>();

        // Register registries
        services.AddSingleton<BrokerRegistry>();
        services.AddSingleton<IBrokerRegistry>(provider => provider.GetRequiredService<BrokerRegistry>());
        services.AddSingleton<BrokerMetadataRegistry>();
        services.AddSingleton<IBrokerMetadataRegistry>(provider => provider.GetRequiredService<BrokerMetadataRegistry>());

        return services;
    }
}
