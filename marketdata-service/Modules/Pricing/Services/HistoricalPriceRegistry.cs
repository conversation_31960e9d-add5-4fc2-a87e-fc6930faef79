using MarketDataService.Modules.Pricing.Interfaces;

namespace MarketDataService.Modules.Pricing.Services;

public class HistoricalPriceRegistry(IEnumerable<IHistoricalPriceProvider> providers) : IHistoricalPriceRegistry
{
    private readonly Dictionary<string, IHistoricalPriceProvider> _providers = CreateProviderDictionary(providers);

    public IHistoricalPriceProvider? GetProvider(string brokerId)
    {
        _providers.TryGetValue(brokerId.ToLower(), out var provider);
        return provider;
    }

    private static Dictionary<string, IHistoricalPriceProvider> CreateProviderDictionary(IEnumerable<IHistoricalPriceProvider> providers)
    {
        var dictionary = new Dictionary<string, IHistoricalPriceProvider>();
        foreach (var provider in providers)
        {
            var key = provider.BrokerId.ToLower();
            if (dictionary.ContainsKey(key))
            {
                throw new ArgumentException($"Duplicate broker ID found: {provider.BrokerId}");
            }
            dictionary[key] = provider;
        }
        return dictionary;
    }
}