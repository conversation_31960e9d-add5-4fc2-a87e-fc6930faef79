using MarketDataService.Configuration;
using MarketDataService.Modules.Core.Services;
using MarketDataService.Modules.Pricing.Interfaces;
using MarketDataService.Modules.Core.Models;
using MarketDataService.Modules.Brokers.Interfaces;
using MarketDataService.Modules.Caching.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;

namespace MarketDataService.Modules.Pricing.Services;

public class PriceService(
    MarketDataContext db,
    IBrokerRegistry registry,
    IRedisCacheService cache,
    IOptions<BrokerSettings> settings,
    ILogger<PriceService> logger) : IPriceService
{
    private readonly MarketDataContext _db = db;
    private readonly IBrokerRegistry _registry = registry;
    private readonly IRedisCacheService _cache = cache;
    private readonly BrokerSettings _settings = settings.Value;
    private readonly ILogger<PriceService> _logger = logger;

    public async Task<MarketData?> GetLatestPriceAsync(string symbol, string? brokerId = null, bool bypassCache = false)
    {
        var cacheKey = $"price:{brokerId}:{symbol.ToUpper()}";

        if (!bypassCache)
        {
            var cached = await _cache.GetAsync<MarketData>(cacheKey);
            if (cached != null) return cached;
        }

        var tried = new HashSet<string>();
        var fallbackOrder = new[] { brokerId, "finnhub", "polygon" };

        foreach (var broker in fallbackOrder.Distinct())
        {
            if (broker != null) tried.Add(broker);
            var provider = broker != null ? _registry.GetProvider(broker) : null;
            if (provider == null) continue;

            var price = await provider.FetchPriceAsync(symbol);
            if (price != null)
            {
                var entry = new MarketData
                {
                    Symbol = symbol.ToUpper(),
                    Price = price.Value,
                    Timestamp = DateTime.UtcNow
                };

                _db.MarketDataEntries.Add(entry);
                await _db.SaveChangesAsync();
                await _cache.SetAsync(cacheKey, entry, TimeSpan.FromMinutes(_settings.CacheMinutes));
                return entry;
            }
        }

        _logger.LogWarning("Failed to fetch price for symbol {Symbol} after trying brokers: {TriedBrokers}",
            symbol, string.Join(", ", tried));
        return null;
    }
}